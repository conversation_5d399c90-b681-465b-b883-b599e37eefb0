<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GatedCNNBlock 可视化详解</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .content {
            padding: 40px;
        }
        .flow-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            margin: 30px 0;
        }
        .block {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            min-width: 200px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }
        .block:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .input-block {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            border-color: #0984e3;
        }
        .norm-block {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            border-color: #e84393;
        }
        .linear-block {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
            color: white;
            border-color: #e17055;
        }
        .split-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .split-block {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            border-color: #6c5ce7;
            flex: 1;
            min-width: 150px;
        }
        .conv-block {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
        }
        .gate-block {
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
        }
        .output-block {
            background: linear-gradient(135deg, #00cec9, #00b894);
            color: white;
            border-color: #00b894;
        }
        .arrow {
            font-size: 24px;
            color: #2d3436;
            margin: 10px 0;
        }
        .arrow-horizontal {
            font-size: 20px;
            color: #2d3436;
            margin: 0 10px;
            align-self: center;
        }
        .dimension-info {
            font-size: 12px;
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
            margin-top: 10px;
            display: inline-block;
        }
        .code-section {
            background: #2d3436;
            color: #ddd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .parameter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .parameter-card {
            background: #f1f2f6;
            border-left: 4px solid #3742fa;
            padding: 20px;
            border-radius: 8px;
        }
        .parameter-card h3 {
            margin-top: 0;
            color: #2f3542;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .residual-connection {
            position: absolute;
            right: -50px;
            top: 50%;
            transform: translateY(-50%);
            background: #ff6b6b;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            white-space: nowrap;
        }
        .math-formula {
            background: #e8f4fd;
            border: 1px solid #3498db;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-family: 'Times New Roman', serif;
            font-size: 16px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 GatedCNNBlock 可视化详解</h1>
            <p>MambaOut核心组件的完整数据流和计算过程</p>
        </div>
        
        <div class="content">
            <div class="highlight">
                <strong>🎯 核心思想：</strong>GatedCNNBlock通过门控机制选择性地传递信息，结合深度可分离卷积和残差连接，实现高效的特征提取。
            </div>

            <h2>📊 完整数据流可视化</h2>
            <div class="flow-container">
                <!-- 输入 -->
                <div class="block input-block">
                    <strong>输入 X</strong>
                    <div class="dimension-info">(B, H, W, C)</div>
                    <div style="margin-top: 10px;">shortcut = X</div>
                </div>
                
                <div class="arrow">↓</div>
                
                <!-- LayerNorm -->
                <div class="block norm-block">
                    <strong>LayerNorm</strong>
                    <div class="dimension-info">(B, H, W, C) → (B, H, W, C)</div>
                    <div style="margin-top: 10px;">标准化输入特征</div>
                </div>
                
                <div class="arrow">↓</div>
                
                <!-- 线性投影 -->
                <div class="block linear-block">
                    <strong>Linear Projection (FC1)</strong>
                    <div class="dimension-info">(B, H, W, C) → (B, H, W, 2×hidden)</div>
                    <div style="margin-top: 10px;">hidden = expansion_ratio × C</div>
                </div>
                
                <div class="arrow">↓</div>
                
                <!-- 分割 -->
                <div style="text-align: center; margin: 20px 0;">
                    <strong style="font-size: 18px;">🔀 三路分割 (Split)</strong>
                </div>
                
                <div class="split-container">
                    <div class="block split-block">
                        <strong>Gate (g)</strong>
                        <div class="dimension-info">(B, H, W, hidden)</div>
                        <div style="margin-top: 10px;">门控信号</div>
                    </div>
                    
                    <div class="block split-block">
                        <strong>Identity (i)</strong>
                        <div class="dimension-info">(B, H, W, hidden-conv_ch)</div>
                        <div style="margin-top: 10px;">直通特征</div>
                    </div>
                    
                    <div class="block split-block">
                        <strong>Conv (c)</strong>
                        <div class="dimension-info">(B, H, W, conv_ch)</div>
                        <div style="margin-top: 10px;">卷积特征</div>
                    </div>
                </div>
                
                <div class="arrow">↓</div>
                
                <!-- 卷积处理 -->
                <div class="block conv-block">
                    <strong>深度可分离卷积</strong>
                    <div class="dimension-info">c: NHWC → NCHW → Conv2d → NHWC</div>
                    <div style="margin-top: 10px;">kernel_size=7, groups=conv_channels</div>
                </div>
                
                <div class="arrow">↓</div>
                
                <!-- 门控机制 -->
                <div class="block gate-block">
                    <strong>🚪 门控机制</strong>
                    <div class="dimension-info">GELU(g) ⊗ Concat([i, c])</div>
                    <div style="margin-top: 10px;">选择性信息传递</div>
                </div>
                
                <div class="arrow">↓</div>
                
                <!-- 输出投影 -->
                <div class="block linear-block">
                    <strong>Output Projection (FC2)</strong>
                    <div class="dimension-info">(B, H, W, hidden) → (B, H, W, C)</div>
                    <div style="margin-top: 10px;">降维回原始维度</div>
                </div>
                
                <div class="arrow">↓</div>
                
                <!-- DropPath -->
                <div class="block norm-block">
                    <strong>DropPath</strong>
                    <div class="dimension-info">随机深度正则化</div>
                </div>
                
                <div class="arrow">↓</div>
                
                <!-- 残差连接 -->
                <div class="block output-block" style="position: relative;">
                    <strong>输出 = DropPath(output) + shortcut</strong>
                    <div class="dimension-info">(B, H, W, C)</div>
                    <div style="margin-top: 10px;">残差连接</div>
                    <div class="residual-connection">+ shortcut</div>
                </div>
            </div>

            <h2>🧮 数学公式表示</h2>
            <div class="math-formula">
                <strong>完整公式：</strong><br>
                Y = DropPath(FC2(GELU(g) ⊗ Concat([i, DWConv(c)]))) + X<br><br>
                其中：g, i, c = Split(FC1(LayerNorm(X)))
            </div>

            <h2>⚙️ 关键参数详解</h2>
            <div class="parameter-grid">
                <div class="parameter-card">
                    <h3>🔢 expansion_ratio</h3>
                    <p><strong>默认值：</strong> 8/3 ≈ 2.67</p>
                    <p><strong>作用：</strong> 控制隐藏层维度扩展倍数</p>
                    <p><strong>计算：</strong> hidden = int(expansion_ratio × dim)</p>
                </div>
                
                <div class="parameter-card">
                    <h3>🎛️ conv_ratio</h3>
                    <p><strong>默认值：</strong> 1.0</p>
                    <p><strong>作用：</strong> 控制参与卷积的通道比例</p>
                    <p><strong>优化：</strong> 部分通道卷积提高效率</p>
                </div>
                
                <div class="parameter-card">
                    <h3>🔍 kernel_size</h3>
                    <p><strong>默认值：</strong> 7</p>
                    <p><strong>作用：</strong> 卷积核大小，控制感受野</p>
                    <p><strong>特点：</strong> 较大核提供更大感受野</p>
                </div>
                
                <div class="parameter-card">
                    <h3>📉 drop_path</h3>
                    <p><strong>作用：</strong> 随机深度正则化</p>
                    <p><strong>训练：</strong> 随机丢弃整个路径</p>
                    <p><strong>推理：</strong> 恒等映射</p>
                </div>
            </div>

            <h2>💡 设计亮点</h2>
            <div class="parameter-grid">
                <div class="parameter-card">
                    <h3>🚪 门控机制</h3>
                    <ul>
                        <li>选择性信息传递</li>
                        <li>类似LSTM的门控思想</li>
                        <li>GELU激活增强非线性</li>
                    </ul>
                </div>
                
                <div class="parameter-card">
                    <h3>🔄 数据格式转换</h3>
                    <ul>
                        <li>NHWC格式便于LayerNorm</li>
                        <li>NCHW格式便于卷积操作</li>
                        <li>自动格式转换优化</li>
                    </ul>
                </div>
                
                <div class="parameter-card">
                    <h3>⚡ 效率优化</h3>
                    <ul>
                        <li>深度可分离卷积减少参数</li>
                        <li>部分通道卷积提高速度</li>
                        <li>残差连接缓解梯度消失</li>
                    </ul>
                </div>
                
                <div class="parameter-card">
                    <h3>🎯 特征处理</h3>
                    <ul>
                        <li>三路分割处理不同特征</li>
                        <li>门控融合多路信息</li>
                        <li>LayerNorm稳定训练</li>
                    </ul>
                </div>
            </div>

            <h2>📝 伪代码实现</h2>
            <div class="code-section">
def GatedCNNBlock_forward(x):
    # x shape: (B, H, W, C)
    shortcut = x
    
    # 1. 标准化
    x = LayerNorm(x)  # (B, H, W, C)
    
    # 2. 线性投影到2倍隐藏维度
    hidden = int(expansion_ratio * C)  # 8/3 * C
    projected = Linear(x, 2 * hidden)  # (B, H, W, 2*hidden)
    
    # 3. 三路分割
    conv_channels = int(conv_ratio * C)
    g = projected[:, :, :, :hidden]                    # 门控
    i = projected[:, :, :, hidden:2*hidden-conv_channels]  # 直通
    c = projected[:, :, :, 2*hidden-conv_channels:]    # 卷积
    
    # 4. 卷积分支处理
    c = permute(c, NHWC_to_NCHW)  # (B, conv_ch, H, W)
    c = DepthwiseConv2d(c, kernel=7, groups=conv_channels)
    c = permute(c, NCHW_to_NHWC)  # (B, H, W, conv_ch)
    
    # 5. 门控机制
    gate_signal = GELU(g)  # (B, H, W, hidden)
    features = Concat([i, c], dim=-1)  # (B, H, W, hidden)
    gated_output = gate_signal * features  # 逐元素相乘
    
    # 6. 输出投影
    output = Linear(gated_output, C)  # (B, H, W, C)
    
    # 7. 随机深度 + 残差连接
    output = DropPath(output)
    return output + shortcut
            </div>

            <div class="highlight">
                <strong>🔑 关键优势：</strong>
                <ul>
                    <li><strong>简单有效：</strong>移除复杂的SSM，保留核心的门控机制</li>
                    <li><strong>计算高效：</strong>深度可分离卷积和部分通道处理</li>
                    <li><strong>训练稳定：</strong>LayerNorm和残差连接保证梯度流</li>
                    <li><strong>灵活配置：</strong>多个超参数支持不同规模需求</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
