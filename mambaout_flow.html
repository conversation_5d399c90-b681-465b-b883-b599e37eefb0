<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>MambaOut 模型端到端流程可视化</title>
  <style>
    :root {
      --bg: #0b1220;
      --panel: #121a2b;
      --panel-2: #0f1727;
      --text: #e6ecf2;
      --muted: #9fb2c8;
      --accent: #6aa6ff;
      --accent-2: #9dd5ff;
      --ok: #36c690;
      --warn: #ffb454;
      --border: #21314e;
    }
    * { box-sizing: border-box; }
    body {
      margin: 0; padding: 32px; font-family: ui-sans-serif, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
      color: var(--text); background: radial-gradient(1200px 600px at 20% -10%, #1b2740 0%, var(--bg) 50%);
    }
    h1, h2, h3 { margin: 0 0 12px; line-height: 1.25; }
    h1 { font-size: 28px; }
    h2 { font-size: 22px; margin-top: 24px; }
    h3 { font-size: 18px; margin-top: 18px; color: var(--accent-2); }
    p { color: var(--muted); line-height: 1.7; }
    a { color: var(--accent); text-decoration: none; }
    a:hover { text-decoration: underline; }

    .container { max-width: 1100px; margin: 0 auto; }
    .panel { background: linear-gradient(180deg, var(--panel), var(--panel-2)); border: 1px solid var(--border); border-radius: 14px; padding: 18px 20px; margin: 14px 0; box-shadow: 0 10px 30px rgba(0,0,0,.25), inset 0 1px 0 rgba(255,255,255,0.03); }
    .muted { color: var(--muted); }

    .kv { display: grid; grid-template-columns: 160px 1fr; gap: 10px 14px; }
    .kv .k { color: #b9c9dd; }

    .controls { display: flex; flex-wrap: wrap; gap: 12px; align-items: center; }
    .controls label { font-size: 14px; color: #b9c9dd; }
    .controls input[type="number"], .controls select {
      background: #0d1424; color: var(--text); border: 1px solid var(--border);
      padding: 8px 10px; border-radius: 10px; outline: none; min-width: 120px;
    }

    .flow {
      display: grid; gap: 10px; grid-template-columns: repeat(4, 1fr);
    }
    .stage, .op {
      background: #0d1424; border: 1px solid var(--border); border-radius: 12px;
      padding: 12px; position: relative;
    }
    .stage h4, .op h4 { margin: 0 0 6px; font-size: 14px; color: var(--accent); }
    .shape { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; font-size: 12px; color: #bcd0e6; }
    .chip { display: inline-block; border: 1px solid var(--border); color: #b9c9dd; background: #0b1322; border-radius: 999px; padding: 2px 8px; font-size: 12px; margin-right: 6px; }

    .diagram { margin-top: 10px; display: grid; grid-template-columns: 1fr; gap: 8px; }
    .box { border: 1px dashed #2a3a5d; border-radius: 10px; padding: 8px 10px; }

    .tag { color: #b9c9dd; }
    details { background: #0c1423; border: 1px solid var(--border); border-radius: 10px; padding: 10px 12px; }
    details summary { cursor: pointer; color: var(--accent-2); font-weight: 600; }
    code, pre { background: #0a1221; color: #d9e7ff; border: 1px solid var(--border); border-radius: 8px; }
    code { padding: 2px 6px; }
    pre { overflow: auto; padding: 12px; }

    .grid-2 { display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; }
    .note { border-left: 3px solid var(--accent); padding-left: 10px; }
    .pill-ok { color: #0f2; background: rgba(54, 198, 144, .15); border-color: rgba(54,198,144,.35); }
    .pill-warn { color: #ffb454; background: rgba(255, 180, 84, .12); border-color: rgba(255, 180, 84, .35); }

    @media (max-width: 960px) { .flow { grid-template-columns: 1fr 1fr; } }
    @media (max-width: 640px) { .flow { grid-template-columns: 1fr; } }
  </style>
</head>
<body>
  <div class="container">
    <h1>MambaOut 模型端到端流程（交互式讲解）</h1>
    <p class="muted">本页面以直观的方式，分步骤展示 MambaOut（Gated CNN）在图像分类中的完整前向与训练/验证流程，包含形状变化、关键算子、核心公式与可切换的模型版本参数。</p>

    <div class="panel">
      <h2>一、流程总览</h2>
      <div class="kv" style="margin-top:8px">
        <div class="k">输入</div>
        <div>图像经过 <span class="tag">Resize</span> → <span class="tag">Crop</span> → <span class="tag">Normalize</span> 预处理后得到张量 <code>[B,3,H,W]</code></div>
        <div class="k">主干网络</div>
        <div>四阶段结构：<span class="tag">Stem 降采样</span> → <span class="tag">Gated CNN 块 × N</span> →（重复：降采样 + 块）</div>
        <div class="k">特征聚合</div>
        <div><span class="tag">全局平均池化</span>（H,W 上求均值）→ <span class="tag">LayerNorm</span></div>
        <div class="k">分类头</div>
        <div>两层 MLP（<span class="tag">fc → GELU → LN → Dropout → fc</span>）输出 logits <code>[B, num_classes]</code></div>
        <div class="k">训练</div>
        <div>AdamW + 余弦退火 + DeiT 风格增广（Mixup/CutMix/Random Erasing/Label Smoothing 等）</div>
        <div class="k">验证</div>
        <div>自动解析输入配置（分辨率/裁剪比例/均值方差），计算 Top-1/Top-5</div>
      </div>
    </div>

    <div class="panel">
      <h2>二、选择模型与输入尺寸</h2>
      <div class="controls">
        <label>模型版本：</label>
        <select id="variant">
          <option value="femto">mambaout_femto（depths=[3,3,9,3], dims=[48,96,192,288]）</option>
          <option value="kobe">mambaout_kobe（depths=[3,3,15,3], dims=[48,96,192,288]）</option>
          <option value="tiny" selected>mambaout_tiny（depths=[3,3,9,3], dims=[96,192,384,576]）</option>
          <option value="small">mambaout_small（depths=[3,4,27,3], dims=[96,192,384,576]）</option>
          <option value="base">mambaout_base（depths=[3,4,27,3], dims=[128,256,512,768]）</option>
        </select>
        <label>输入尺寸（正方形，默认 224）：</label>
        <input id="inputSize" type="number" min="32" max="1024" step="8" value="224" />
        <span class="chip">布局：channels-first 进入 → 内部多数算子使用 channels-last</span>
      </div>
    </div>

    <div class="panel">
      <h2>三、端到端形状与算子</h2>
      <div id="flow" class="flow"></div>
      <p class="muted" style="margin-top:6px">说明：<span class="tag">Stem</span> 将分辨率两次 /2，即总 /4；随后每个 <span class="tag">Downsample</span> 再 /2。各阶段 Gated CNN 块不改变分辨率，仅在通道维与空间混合上做计算。</p>
    </div>

    <div class="panel">
      <h2>四、Gated CNN 块（核心）</h2>
      <div class="grid-2">
        <div>
          <h3>结构要点</h3>
          <ul class="muted">
            <li><b>输入/输出形状一致</b>：<code>[B, H, W, C]</code>，残差连接</li>
            <li><b>线性扩展 + 三路切分</b>：得到门分支 g、直通分支 i、卷积分支 c</li>
            <li><b>部分通道 depthwise 卷积</b>：仅对 c 做深度卷积（空间混合，通道不混）</li>
            <li><b>门控融合</b>：<code>GELU(g) ⊙ concat(i, conv(c))</code></li>
            <li><b>回投 + DropPath + 残差</b>：线性回到 C，随机深度，残差相加</li>
          </ul>
          <details>
            <summary>关键公式（抽象化）</summary>
            <pre>
设 W1 为扩展线性层，W2 为回投线性层，DWConv 为 depthwise 卷积，φ 为 GELU。

[g, i, c] = split(W1(LN(x)))
c' = DWConv(c)
y = x + DropPath(W2( φ(g) ⊙ concat(i, c') ))
            </pre>
          </details>
        </div>
        <div>
          <h3>伪代码（对应实现）</h3>
          <pre>
# 输入 x: [B, H, W, C]
shortcut = x
x = LayerNorm(x)
# 展开到 2*hidden 并三路切分
[g, i, c] = split(Linear_expand(x), sizes=[hidden, hidden - convC, convC])
# 仅对 c 做 depthwise conv（空间混合）
c = to BCHW(c)
c = DepthwiseConv(k=7, p=3)
c = to BHWC(c)
# 门控融合 + 回投
x = Linear_project(GELU(g) * concat(i, c))
# 随机深度 + 残差
x = DropPath(x)
return x + shortcut
          </pre>
          <p class="muted">超参：<code>expansion_ratio=8/3</code>，<code>conv_ratio</code>（默认 1.0，可 &lt;1 提升效率），<code>kernel_size=7</code>，<code>drop_path</code>。</p>
        </div>
      </div>
    </div>

    <div class="panel">
      <h2>五、训练与验证流水</h2>
      <div class="grid-2">
        <div class="op">
          <h4>训练（train.py）</h4>
          <div class="diagram">
            <div class="box"><b>数据</b>：ImageFolder → <span class="tag">create_dataset / create_loader</span></div>
            <div class="box"><b>增广</b>：DeiT 风格（RandAugment、Mixup、CutMix、Random Erasing、Label Smoothing）</div>
            <div class="box"><b>优化</b>：AdamW，余弦退火（含 warmup）、AMP（可选）、梯度累积（可选）</div>
            <div class="box"><b>评估</b>：每 epoch 在验证集计算 Top-1/Top-5，保存最佳模型</div>
          </div>
        </div>
        <div class="op">
          <h4>验证（validate.py）</h4>
          <div class="diagram">
            <div class="box"><b>载入模型</b>：预训练或指定 checkpoint</div>
            <div class="box"><b>解析配置</b>：<span class="tag">resolve_data_config</span>（分辨率/裁剪/均值方差/插值）</div>
            <div class="box"><b>前向推理</b>：DataLoader → 模型 → 指标（Top-1/Top-5）</div>
            <div class="box"><b>可选</b>：Test-time pool / 真实标签评估</div>
          </div>
        </div>
      </div>
      <p class="note muted" style="margin-top:8px">提示：Windows 推荐单卡训练；多卡训练（分布式 NCCL）请在 Linux/WSL 环境使用。</p>
    </div>

    <div class="panel">
      <h2>六、与因果/自回归的关系</h2>
      <p class="muted">MambaOut 的 Gated CNN 用 <b>全可见</b> 的空间混合（局部卷积 + 门控），不引入自回归/因果约束或状态空间。对 ImageNet 这类 <b>理解任务</b>（非自回归）更匹配；将 ViT 的注意力改成因果模式会掉点，说明因果混合对该任务并非必要。</p>
    </div>

    <div class="panel">
      <h2>七、模型版本参数</h2>
      <div class="kv">
        <div class="k">femto</div>
        <div>depths=[3,3,9,3]，dims=[48,96,192,288]</div>
        <div class="k">kobe</div>
        <div>depths=[3,3,15,3]，dims=[48,96,192,288]</div>
        <div class="k">tiny</div>
        <div>depths=[3,3,9,3]，dims=[96,192,384,576]</div>
        <div class="k">small</div>
        <div>depths=[3,4,27,3]，dims=[96,192,384,576]</div>
        <div class="k">base</div>
        <div>depths=[3,4,27,3]，dims=[128,256,512,768]</div>
      </div>
    </div>

    <div class="panel">
      <h2>八、参考与致谢</h2>
      <p class="muted">实现基于 <span class="tag">timm</span>、MetaFormer、InceptionNeXt 等；论文：MambaOut: Do We Really Need Mamba for Vision?（CVPR 2025）。</p>
    </div>
  </div>

  <script>
    const variants = {
      femto: { depths: [3,3,9,3], dims: [48,96,192,288] },
      kobe:  { depths: [3,3,15,3], dims: [48,96,192,288] },
      tiny:  { depths: [3,3,9,3], dims: [96,192,384,576] },
      small: { depths: [3,4,27,3], dims: [96,192,384,576] },
      base:  { depths: [3,4,27,3], dims: [128,256,512,768] },
    };

    const by2 = v => Math.max(1, Math.floor(v / 2));

    function computeStages(inputSz, dims) {
      // 输入 BCHW：224 → Stem(2x2) → Stem(2x2) → 56
      const s0 = by2(by2(inputSz));
      const s1 = by2(s0);
      const s2 = by2(s1);
      const s3 = by2(s2);
      return [s0, s1, s2, s3];
    }

    function box(title, shape, badges = []) {
      return `
        <div class="stage">
          <h4>${title}</h4>
          <div class="shape">形状：<code>${shape}</code></div>
          <div style="margin-top:6px">${badges.map(b => `<span class=\"chip\">${b}</span>`).join('')}</div>
        </div>
      `;
    }

    function op(title, lines = []) {
      return `
        <div class="op">
          <h4>${title}</h4>
          <div class="diagram">
            ${lines.map(l => `<div class=\"box\">${l}</div>`).join('')}
          </div>
        </div>
      `;
    }

    function render() {
      const sel = document.getElementById('variant').value;
      const inputSz = parseInt(document.getElementById('inputSize').value || '224', 10);
      const { depths, dims } = variants[sel];
      const flow = document.getElementById('flow');

      const [h0, h1, h2, h3] = computeStages(inputSz, dims);
      const c0 = dims[0], c1 = dims[1], c2 = dims[2], c3 = dims[3];

      const nodes = [];
      nodes.push(box('输入（预处理后）', `[B, 3, ${inputSz}, ${inputSz}]`, ['Resize/Crop/Normalize']));
      nodes.push(op('Stem 降采样', [
        `<b>Conv2d</b> k=3,s=2 → [B, ${Math.max(1, Math.floor(inputSz/2))}, ${Math.max(1, Math.floor(inputSz/2))}]`,
        `<b>LayerNorm + GELU</b>`,
        `<b>Conv2d</b> k=3,s=2 → [B, ${h0}, ${h0}]`,
        `<b>LayerNorm</b>`
      ]));
      nodes.push(box(`Stage 0（块 × ${depths[0]}）`, `[B, ${h0}, ${h0}, ${c0}]`, ['Gated CNN']));
      nodes.push(op('Downsample 1', [ `<b>Conv2d</b> k=3,s=2 + LN → [B, ${h1}, ${h1}, ${c1}]` ]));
      nodes.push(box(`Stage 1（块 × ${depths[1]}）`, `[B, ${h1}, ${h1}, ${c1}]`, ['Gated CNN']));
      nodes.push(op('Downsample 2', [ `<b>Conv2d</b> k=3,s=2 + LN → [B, ${h2}, ${h2}, ${c2}]` ]));
      nodes.push(box(`Stage 2（块 × ${depths[2]}）`, `[B, ${h2}, ${h2}, ${c2}]`, ['Gated CNN']));
      nodes.push(op('Downsample 3', [ `<b>Conv2d</b> k=3,s=2 + LN → [B, ${h3}, ${h3}, ${c3}]` ]));
      nodes.push(box(`Stage 3（块 × ${depths[3]}）`, `[B, ${h3}, ${h3}, ${c3}]`, ['Gated CNN']));
      nodes.push(op('特征聚合', [ `<b>GlobalAvgPool</b> → [B, ${c3}]`, `<b>LayerNorm</b>` ]));
      nodes.push(box('分类头（MLP）', `[B, num_classes]`, ['fc → GELU → LN → Dropout → fc']));

      flow.innerHTML = nodes.join('');
    }

    document.getElementById('variant').addEventListener('change', render);
    document.getElementById('inputSize').addEventListener('input', render);
    render();
  </script>
</body>
</html> 